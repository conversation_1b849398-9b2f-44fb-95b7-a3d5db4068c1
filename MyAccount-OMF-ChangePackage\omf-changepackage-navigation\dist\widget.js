/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("bwtk"), require("omf-changepackage-components"), require("react"), require("react-redux"), require("react-router-dom"), require("react-intl"), require("react-dom"), require("redux"), require("redux-actions"), require("redux-observable"), require("rxjs"));
	else if(typeof define === 'function' && define.amd)
		define(["bwtk", "omf-changepackage-components", "react", "react-redux", "react-router-dom", "react-intl", "react-dom", "redux", "redux-actions", "redux-observable", "rxjs"], factory);
	else {
		var a = typeof exports === 'object' ? factory(require("bwtk"), require("omf-changepackage-components"), require("react"), require("react-redux"), require("react-router-dom"), require("react-intl"), require("react-dom"), require("redux"), require("redux-actions"), require("redux-observable"), require("rxjs")) : factory(root["bwtk"], root["OMFChangepackageComponents"], root["React"], root["ReactRedux"], root["ReactRouterDOM"], root["ReactIntl"], root["ReactDOM"], root["Redux"], root["ReduxActions"], root["ReduxObservable"], root["rxjs"]);
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(self, function(__WEBPACK_EXTERNAL_MODULE_bwtk__, __WEBPACK_EXTERNAL_MODULE_omf_changepackage_components__, __WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_react_redux__, __WEBPACK_EXTERNAL_MODULE_react_router_dom__, __WEBPACK_EXTERNAL_MODULE_react_intl__, __WEBPACK_EXTERNAL_MODULE_react_dom__, __WEBPACK_EXTERNAL_MODULE_redux__, __WEBPACK_EXTERNAL_MODULE_redux_actions__, __WEBPACK_EXTERNAL_MODULE_redux_observable__, __WEBPACK_EXTERNAL_MODULE_rxjs__) {
return /******/ (function() { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "../src/Widget.tsx":
/*!**************************************!*\
  !*** ../src/Widget.tsx + 27 modules ***!
  \**************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("{// ESM COMPAT FLAG\n__webpack_require__.r(__webpack_exports__);\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ Widget; }\n});\n\n// NAMESPACE OBJECT: ../src/store/Actions.ts\nvar Actions_namespaceObject = {};\n__webpack_require__.r(Actions_namespaceObject);\n__webpack_require__.d(Actions_namespaceObject, {\n  checkRestrictions: function() { return checkRestrictions; },\n  setFlowType: function() { return setFlowType; },\n  setSummaryTotals: function() { return setSummaryTotals; }\n});\n\n;// ./tslib/tslib.es6.mjs\n/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nfunction __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nvar __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nfunction __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nfunction __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nfunction __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nfunction __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nfunction __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nfunction __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nfunction __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nfunction __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nfunction __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nfunction __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nvar __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nfunction __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nfunction __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nfunction __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nfunction __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nfunction __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nfunction __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nfunction __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nfunction __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nfunction __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nfunction __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nfunction __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nfunction __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nfunction __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nfunction __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nfunction __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nfunction __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nfunction __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nfunction __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nfunction __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\n/* harmony default export */ var tslib_es6 = ({\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n});\n\n// EXTERNAL MODULE: external {\"root\":\"bwtk\",\"commonjs2\":\"bwtk\",\"commonjs\":\"bwtk\",\"amd\":\"bwtk\"}\nvar external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_ = __webpack_require__(\"bwtk\");\n// EXTERNAL MODULE: external {\"root\":\"OMFChangepackageComponents\",\"commonjs2\":\"omf-changepackage-components\",\"commonjs\":\"omf-changepackage-components\",\"amd\":\"omf-changepackage-components\"}\nvar external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_ = __webpack_require__(\"omf-changepackage-components\");\n// EXTERNAL MODULE: external {\"root\":\"React\",\"commonjs2\":\"react\",\"commonjs\":\"react\",\"amd\":\"react\"}\nvar external_root_React_commonjs2_react_commonjs_react_amd_react_ = __webpack_require__(\"react\");\n// EXTERNAL MODULE: external {\"root\":\"ReactRedux\",\"commonjs2\":\"react-redux\",\"commonjs\":\"react-redux\",\"amd\":\"react-redux\"}\nvar external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_ = __webpack_require__(\"react-redux\");\n// EXTERNAL MODULE: external {\"root\":\"ReactRouterDOM\",\"commonjs2\":\"react-router-dom\",\"commonjs\":\"react-router-dom\",\"amd\":\"react-router-dom\"}\nvar external_root_ReactRouterDOM_commonjs2_react_router_dom_commonjs_react_router_dom_amd_react_router_dom_ = __webpack_require__(\"react-router-dom\");\n;// ../src/utils/History.ts\nvar _a;\n\nvar _history = null;\nvar _tvhistory = null;\nvar _isAppointmentVisited = false;\nvar Routes = (_a = {},\n    _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.INTERNET] = [\n        \"/Changepackage/Internet\",\n        \"/Changepackage/Internet/Appointment\",\n        \"/Changepackage/Internet/Review\",\n        \"/Changepackage/Internet/Confirmation\"\n    ],\n    _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.TV] = [\n        \"/Changepackage/TV\",\n        \"/Changepackage/TV/Review\",\n        \"/Changepackage/TV/Confirmation\"\n    ],\n    _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.ADDTV] = [\n        \"/Add/TV\",\n        \"/Add/TV/Review\",\n        \"/Add/TV/Confirmation\"\n    ],\n    _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.BUNDLE] = [\n        \"/Bundle/Internet\",\n        \"/Bundle/Internet/Appointment\",\n        \"/Bundle/TV\",\n        \"/Bundle/Review\",\n        \"/Bundle/Confirmation\"\n    ],\n    _a);\nfunction setHistoryProvider(history) {\n    _history = _history || history;\n}\nfunction setTVHistoryProvider(history) {\n    _tvhistory = history || _tvhistory;\n}\nfunction useHistory() {\n    return _history;\n}\nfunction useTVHistory() {\n    return _tvhistory;\n}\nfunction enableAppointementRoute() {\n    sessionStorage.setItem(\"omf:hasAppointmentRoute\", \"yes\");\n}\nfunction getPageName(pathname) {\n    switch (true) {\n        case pathname.indexOf(\"Review\") > 0: return \"REVIEW\";\n        case pathname.indexOf(\"Confirm\") > 0: return \"CONFIRMATION\";\n        case pathname.indexOf(\"Appoint\") > 0: return \"APPOINTMENT\";\n        case pathname.indexOf(\"Internet\") > 0: return \"INTERNET\";\n        case pathname.indexOf(\"TV\") > 0: return \"TV\";\n        default: return \"\";\n    }\n}\nfunction setAppointmentVisited() {\n    _isAppointmentVisited = true;\n}\nfunction checkAppointmentVisited() {\n    return _isAppointmentVisited;\n}\n\n// EXTERNAL MODULE: external {\"root\":\"ReactIntl\",\"commonjs2\":\"react-intl\",\"commonjs\":\"react-intl\",\"amd\":\"react-intl\"}\nvar external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_ = __webpack_require__(\"react-intl\");\n;// ../src/Localization.ts\n\n\n\nvar BaseLocalization = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.BaseLocalization;\nvar Localization = (function (_super) {\n    __extends(Localization, _super);\n    function Localization() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Localization_1 = Localization;\n    Localization.getLocalizedString = function (id) {\n        Localization_1.Instance = Localization_1.Instance || external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.ServiceLocator.instance.getService(external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonServices.Localization);\n        var instance = Localization_1.Instance;\n        return instance ? instance.getLocalizedString(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetName.NAVIGATION, id, instance.locale) : id;\n    };\n    var Localization_1;\n    Localization.Instance = null;\n    Localization = Localization_1 = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable\n    ], Localization);\n    return Localization;\n}(BaseLocalization));\n\n\n;// ../src/views/modals/ApplicationLogout.tsx\n\n\n\n\n\nvar Modal = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Modal;\nvar ModalId = \"APPLICATION_LOGOUT\";\nvar Component = function (_a) {\n    var onContinueClick = _a.onContinueClick, closeLightbox = _a.closeLightbox;\n    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Modal, { modalId: ModalId, onShown: function () {\n            external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture().trackFragment({\n                id: \"logoutLightbox\",\n                s_oAPT: {\n                    actionId: 104\n                },\n                s_oPRM: Localization.getLocalizedString(\"APPLICATION_LOGOUT_TITLE\"),\n                s_oLBC: Localization.getLocalizedString(\"APPLICATION_LOGOUT_TEXT\")\n            });\n        }, title: external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"APPLICATION_LOGOUT_TITLE\" }) },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"pad-30\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: \"APPLICATION_LOGOUT_TEXT\" })),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer1 bgGrayLight6\", \"aria-hidden\": \"true\" }),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"button\", { id: \"APP_LOGOUT_CONTINUE\", className: \"btn btn-primary fill-xs\", onClick: onContinueClick },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"APPLICATION_LOGOUT_CONTINUE\" })),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"vSpacer15\", \"aria-hidden\": \"true\" }),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"button\", { id: \"APP_LOGOUT_CLOSE\", className: \"btn btn-default fill-xs\", onClick: closeLightbox },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"APPLICATION_LOGOUT_CLOSE\" }))));\n};\nvar ApplicationLogoutLightbox = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.connect)(function (_a) { return ({}); }, function (dispatch) { return ({\n    onContinueClick: function () {\n        external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture().trackAction({\n            id: \"logoutLightbox\",\n            s_oAPT: {\n                actionId: 647\n            },\n            s_oBTN: {\n                ref: \"APP_LOGOUT_CONTINUE\"\n            }\n        });\n        dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.applicationLogout());\n    },\n    closeLightbox: function () {\n        external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture().trackAction({\n            id: \"logoutLightbox\",\n            s_oAPT: {\n                actionId: 647\n            },\n            s_oBTN: {\n                ref: \"APP_LOGOUT_CLOSE\"\n            }\n        });\n        dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.closeLightbox(ModalId));\n    },\n}); })(Component);\n\n;// ../src/views/footer/index.tsx\n\n\n\n\n\nvar footer_Component = function (_a) {\n    var onLogoutClick = _a.onLogoutClick;\n    return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Context, null, function (_a) {\n        var linkURL = _a.config.linkURL;\n        return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"footer\", { className: \"accss-focus-outline-override-grey-bg\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"a\", { id: \"skipToMain\", href: \"#mainContent\", className: \"skip-to-main-link\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Skip to main content\" })),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"simplified-footer pad-15-top pad-30-top-xs container container-fluid flex flex-justify-space-between flexCol-xs pad-20-left pad-20-right\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flex-vCenter\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"ul\", { className: \"footer-links flex list-unstyled no-margin flexCol-xs\" },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"li\", { className: \"width-100-percent-xs noBorder\" },\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"a\", { id: \"privacy\", href: linkURL.privacyURL, className: \"txtSize14 no-break-white-space block-xs txtCenter-xs sans-serif margin-20-right links-blue-on-bg-gray\" },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Privacy\" }))),\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"li\", { className: \"width-100-percent-xs noBorder\" },\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"a\", { id: \"legal_context\", href: linkURL.legalURL, className: \"txtSize14 no-break-white-space block-xs txtCenter-xs sans-serif margin-20-right links-blue-on-bg-gray\" },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Legal\" }))),\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"li\", { className: \"width-100-percent-xs\" },\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"a\", { id: \"feedback\", href: linkURL.feedbackURL, className: \"txtSize14 no-break-white-space block-xs txtCenter-xs sans-serif margin-20-right links-blue-on-bg-gray\" },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"FEEDBACK\" })))),\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer15\", \"aria-hidden\": \"true\" }),\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"txtSize14 txtCenter-xs \" },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Copyright\" }))),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flex flexCol-xs\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"spacer30 d-block d-sm-none\", \"aria-hidden\": \"true\" }),\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"button\", { id: \"footerLogout\", onClick: function () { return onLogoutClick(\"footerLogout\"); }, className: \"btn btn-secondary flex middle-align-self line-height-1\", type: \"button\" },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Log out\" })),\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"vSpacer30 hidden-m\" }),\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"spacer30 d-block d-sm-none\", \"aria-hidden\": \"true\" }),\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"width-100-percent-xs txtCenter-xs\" },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"img\", { className: \"img-responsive logo-footer\", role: \"link\", tabIndex: 0, src: linkURL.entrustIMGURL, alt: \"Entrust label\" })))),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer40\", \"aria-hidden\": \"true\" })));\n    }));\n};\nvar Footer = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.connect)(function (_a) { return ({}); }, function (dispatch) { return ({\n    onLogoutClick: function (id) { return dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.openLightbox({ lightboxId: ModalId, data: { relativeId: id } })); },\n}); })(footer_Component);\n\n;// ../src/views/modals/ApplicationExit.tsx\n\n\n\n\n\nvar ApplicationExit_Modal = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Modal;\nvar ApplicationExit_ModalId = \"APPLICATION_EXIT\";\nvar ApplicationExit_Component = function (_a) {\n    var onContinueClick = _a.onContinueClick, closeLightbox = _a.closeLightbox;\n    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(ApplicationExit_Modal, { modalId: ApplicationExit_ModalId, onShown: function () {\n            external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture().trackFragment({\n                id: \"exitLightbox\",\n                s_oAPT: {\n                    actionId: 104\n                },\n                s_oPRM: Localization.getLocalizedString(\"APPLICATION_EXIT_TITLE\"),\n                s_oLBC: Localization.getLocalizedString(\"APPLICATION_EXIT_TEXT\")\n            });\n        }, title: external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"APPLICATION_EXIT_TITLE\" }) },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { id: \"APPLICATION_EXIT_TEXT\", className: \"pad-30 pad-15-left-right-xs\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: \"APPLICATION_EXIT_TEXT\" })),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"button\", { id: \"APP_EXIT_CLOSE\", className: \"btn btn-primary fill-xs\", onClick: closeLightbox },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"APPLICATION_EXIT_CLOSE\" })),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"vSpacer15\", \"aria-hidden\": \"true\" }),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"button\", { id: \"APP_EXIT_CONTINUE\", className: \"btn btn-default fill-xs\", onClick: onContinueClick },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"APPLICATION_EXIT_CONTINUE\" }))));\n};\nvar ApplicationExitLightbox = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.connect)(function (_a) { return ({}); }, function (dispatch) { return ({\n    onContinueClick: function () {\n        external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture().trackAction({\n            id: \"exitLightbox\",\n            s_oAPT: {\n                actionId: 647\n            },\n            s_oBTN: {\n                ref: \"APP_EXIT_CONTINUE\"\n            }\n        });\n        dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.applicationExit());\n    },\n    closeLightbox: function () {\n        external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture().trackAction({\n            id: \"exitLightbox\",\n            s_oAPT: {\n                actionId: 647\n            },\n            s_oBTN: {\n                ref: \"APP_EXIT_CLOSE\"\n            }\n        });\n        dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.closeLightbox(ApplicationExit_ModalId));\n    },\n}); })(ApplicationExit_Component);\n\n;// ../src/views/header/index.tsx\n\n\n\n\n\n\nfunction normalize(txt) {\n    return (txt || \"\").replace(/[\\W_]+/g, \"\").toUpperCase();\n}\nvar header_Component = function (_a) {\n    var flowType = _a.flowType, pathname = _a.location.pathname, onBackClick = _a.onBackClick, onExitClick = _a.onExitClick;\n    var isConfirmationStep = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.getPageRoute() === external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.CONFIRMATION;\n    var path = getPageName(pathname);\n    var key = \"\".concat(path, \"_AT_\").concat(normalize(flowType)).concat([external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.REVIEW, external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.CONFIRMATION].indexOf(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.getPageRoute()) > -1 &&\n        checkAppointmentVisited() ? \"+\" : \"\");\n    var backLabel = (window.location.pathname.toLowerCase() === \"/ordering/changepackage/internet/review\") ? \"Back to step 1\" : (window.location.pathname.toLowerCase() === \"/ordering/changepackage/internet\") ? \"Back to Overview\" : \"Back\";\n    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Context, null, function (_a) {\n        var linkURL = _a.config.linkURL;\n        return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"header\", { className: \"bgPrimary simplified-header container-flex-box-wrap\", role: \"banner\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"container container-fluid container-flex-box-wrap flex-justify-space-between accss-focus-outline-override-red-bg\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"page-back-button container-flex-box-wrap fullHeight align-items-center flex\" }, !isConfirmationStep && external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"a\", { id: \"back\", onClick: function (e) { return onBackClick(e, \"back\"); }, \"aria-label\": backLabel, href: linkURL.exitURL, className: \"responsive-simplified-header-back txtDecorationNoneHover txtWhite\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"virgin-icon icon-Left_arrow txtSize15 inlineBlock\", \"aria-hidden\": \"true\" }),\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"txtSize14 hidden-m margin-10-left txtDecoration_hover\" },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Back\" })))),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"page-heading container-flex-box-wrap fullHeight overflow-ellipsis-parent container-flex-grow-fill justify-center\", \"aria-live\": \"assertive\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"middle-align-self overflow-ellipsis\" },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"h1\", { className: \"virginUltraReg txtWhite no-margin overflow-ellipsis txtCenter txtSize22 txtUppercase\" },\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: \"PAGE_NAME_FOR_\".concat(key) })),\n                        !isConfirmationStep && external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: \"STEP_COUNT_FOR_\".concat(key) }, function (txt) { return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Visible, { when: !!txt && txt !== \"STEP_COUNT_FOR_\".concat(key) },\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"p\", { className: \"txtWhite txtSize14 no-margin-bottom sans-serif txtCenter header-steps\", dangerouslySetInnerHTML: { __html: txt } })); }))),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"page-right-button flex-vCenter d-none d-md-flex d-lg-flex\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"button\", { id: \"exit\", onClick: function () { return onExitClick(\"exit\", isConfirmationStep); }, \"data-href\": linkURL.exitURL, className: \"btn btn-secondary-inverted margin-5-right\" },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"EXIT_CTA\" })))));\n    });\n};\nvar Header = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.connect)(function (_a) {\n    var routes = _a.routes;\n    return ({ routes: routes });\n}, function (dispatch) { return ({\n    onBackClick: function (e, ref) {\n        e.preventDefault();\n        e.stopPropagation();\n        dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.historyBack(ref));\n    },\n    onExitClick: function (id, isConfirmationStep) {\n        if (isConfirmationStep)\n            dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.applicationExit());\n        else\n            dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.openLightbox({ lightboxId: ApplicationExit_ModalId, data: { relativeId: id } }));\n    }\n}); })(header_Component);\n\n;// ../src/views/modals/ApplicationReset.tsx\n\n\n\n\n\nvar ApplicationReset_Modal = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Modal;\nvar ApplicationReset_ModalId = \"APPLICATION_RESET\";\nvar ApplicationReset_Component = function (_a) {\n    var onContinueClick = _a.onContinueClick, closeLightbox = _a.closeLightbox;\n    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(ApplicationReset_Modal, { modalId: ApplicationReset_ModalId, onShown: function () {\n            external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture().trackFragment({\n                id: \"exitLightbox\",\n                s_oAPT: {\n                    actionId: 104\n                },\n                s_oPRM: Localization.getLocalizedString(\"APPLICATION_RESET_TITLE\"),\n                s_oLBC: Localization.getLocalizedString(\"APPLICATION_RESET_TEXT\")\n            });\n        }, title: external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"APPLICATION_RESET_TITLE\" }) },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"pad-30\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: \"APPLICATION_RESET_TEXT\" })),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer1 bgGrayLight6\", \"aria-hidden\": \"true\" }),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"button\", { id: \"APP_RESET_CONTINUE\", className: \"btn btn-primary fill-xs\", onClick: onContinueClick },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"APPLICATION_RESET_CONTINUE\" })),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"vSpacer15\", \"aria-hidden\": \"true\" }),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"button\", { id: \"APP_RESET_CLOSE\", className: \"btn btn-default fill-xs\", onClick: closeLightbox },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"APPLICATION_RESET_CLOSE\" }))));\n};\nvar ApplicationResetLightbox = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.connect)(function (_a) { return ({}); }, function (dispatch) { return ({\n    onContinueClick: function () { return dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.applicationReset()); },\n    closeLightbox: function () { return dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.closeLightbox(ApplicationReset_ModalId)); },\n}); })(ApplicationReset_Component);\n\n;// ../src/views/modals/Summary.tsx\n\n\n\n\n\nvar Summary_Modal = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Modal;\nvar Summary_ModalId = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EModals.PREVIEWMODAL;\nvar Summary_Component = function (_a) {\n    var isOpen = _a.isOpen, summaryAction = _a.summaryAction, isContinueEnabled = _a.isContinueEnabled, onContinueClick = _a.onContinueClick, closeLightbox = _a.closeLightbox, dismissLightbox = _a.dismissLightbox;\n    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Summary_Modal, { modalId: Summary_ModalId, className: \"do-not-center-in\", onDismiss: dismissLightbox, title: external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"\".concat(Summary_ModalId, \"_TITLE\") }) },\n        isOpen && external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.WidgetLoader, { widget: external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetName.PREVIEW, mode: external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EReviewMode.Summary, summaryAPI: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summaryAction, \"href\") }),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer30\", \"aria-hidden\": \"true\" }),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer1 bgGrayLight6\", \"aria-hidden\": \"true\" }),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"button\", { id: \"BUTTON_CONTINUE_\".concat(Summary_ModalId), disabled: !isContinueEnabled, className: \"btn btn-primary fill-xs\", onClick: onContinueClick },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"\".concat(Summary_ModalId, \"_CONTINUE\") })),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"vSpacer15\", \"aria-hidden\": \"true\" }),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"button\", { id: \"BUTTON_CLOSE_\".concat(Summary_ModalId), className: \"btn btn-secondary fill-xs\", onClick: closeLightbox },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"\".concat(Summary_ModalId, \"_CLOSE\") }))));\n};\nvar PreviewLightbox = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.connect)(function (_a) {\n    var lightboxData = _a.lightboxData, summary = _a.summary;\n    return ({\n        summaryAction: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"summaryAction\", null),\n        isContinueEnabled: !!(0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"nextAction\", false),\n        isOpen: lightboxData && lightboxData.lightbox === Summary_ModalId\n    });\n}, function (dispatch) { return ({\n    onContinueClick: function () {\n        dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.closeLightbox(Summary_ModalId));\n        dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.broadcastUpdate(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.onContinue()));\n    },\n    closeLightbox: function () {\n        external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture().trackAction({\n            id: \"previewLightbox\",\n            s_oAPT: {\n                actionId: 647\n            },\n            s_oBTN: {\n                ref: \"BUTTON_CLOSE_\".concat(Summary_ModalId)\n            }\n        });\n        dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.closeLightbox(Summary_ModalId));\n    },\n    dismissLightbox: function () {\n        external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture().trackAction({\n            id: \"previewLightbox\",\n            s_oAPT: {\n                actionId: 647\n            },\n            s_oBTN: \"Close\"\n        });\n        dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setlightboxData(\"\"));\n    }\n}); })(Summary_Component);\n\n;// ../src/views/pages/Appointment.tsx\n\n\n\n\nvar Appointment = function (_a) {\n    var title = _a.title;\n    external_root_React_commonjs2_react_commonjs_react_amd_react_.useEffect(function () {\n        enableAppointementRoute();\n        document.title = \"\".concat(title, \" - \").concat(document.title);\n    }, []);\n    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.WidgetLoader, { widget: external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetName.APPOINTMENT });\n};\n\n;// ../src/views/pages/Confirmation.tsx\n\n\n\nvar Confirmation = function (_a) {\n    var title = _a.title;\n    document.title = \"\".concat(title, \" - \").concat(document.title);\n    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.WidgetLoader, { widget: external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetName.CONFIRMATION, mode: external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.CONFIRMATION });\n};\n\n;// ../src/views/pages/Internet.tsx\n\n\n\nvar Internet = function (_a) {\n    var title = _a.title;\n    document.title = \"\".concat(title, \" - \").concat(document.title);\n    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.WidgetLoader, { widget: external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetName.INTERNET });\n};\n\n;// ../src/views/pages/Review.tsx\n\n\n\nvar Review = function (_a) {\n    var title = _a.title;\n    document.title = \"\".concat(title, \" - \").concat(document.title);\n    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.WidgetLoader, { widget: external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetName.REVIEW, mode: external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.REVIEW });\n};\n\n;// ../src/views/pages/TV.tsx\n\n\n\nvar TV = function (_a) {\n    var title = _a.title;\n    document.title = \"\".concat(title, \" - \").concat(document.title);\n    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.WidgetLoader, { widget: external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetName.TV });\n};\n\n// EXTERNAL MODULE: external {\"root\":\"ReactDOM\",\"commonjs2\":\"react-dom\",\"commonjs\":\"react-dom\",\"amd\":\"react-dom\"}\nvar external_root_ReactDOM_commonjs2_react_dom_commonjs_react_dom_amd_react_dom_ = __webpack_require__(\"react-dom\");\n;// ../src/views/summary/TvSummaryPortal.tsx\n\n\n\nvar Visible = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Visible, Currency = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Currency;\nvar TVSummaryPortal = function (_a) {\n    var summary = _a.summary, isContinueEnabled = _a.isContinueEnabled, onContinueClick = _a.onContinueClick;\n    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_React_commonjs2_react_commonjs_react_amd_react_.Fragment, null,\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"TV\", false) },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"TV.currentPrice\") },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"virgin-menu-dockbar flexStatic flexJustifyBetween-sm bgBlack\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"p\", { className: \"noMargin txtSize12 flexGrow txtWhite\" },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"CurrentTV\" })),\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"virginUltraReg txtCurrency txtSize28 pad-5-top txtWhite\" },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Currency, { value: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"TV.currentPrice.price\", 0), monthly: true, prefixClassName: \"txtSize16 txtUppercase\", fractionClassName: \"txtSize16\" })))),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"TV.newPrice\") },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"virgin-menu-dockbar flexStatic bgOrange flexJustifyBetween-sm\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"p\", { className: \"noMargin txtSize12 flexGrow txtWhite\" },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"NewTV\" })),\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"virginUltraReg txtCurrency txtSize28 pad-5-top txtWhite\" },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Currency, { value: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"TV.newPrice.price\", 0), monthly: true, prefixClassName: \"txtSize16 txtUppercase\", fractionClassName: \"txtSize16\" }))))),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexBlock preview-btn bgBlack flexJustify pad-25-top pad-25-bottom\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"button\", { onClick: onContinueClick, disabled: !isContinueEnabled, id: \"mobileTVContinue\", className: \"btn btn-primary txtWhite txtSize16 relative \".concat(isContinueEnabled ? \"\" : \"disabled\") },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Review changes\" }),\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"productOfferingCount\", false) },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"bgWhite txtSize12 pointer sans-serif txtBold txtGray txtCenter dockbar-notification\" },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", null, (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"productOfferingCount\", 0)))))));\n};\n/* harmony default export */ var TvSummaryPortal = (TVSummaryPortal);\n\n;// ../src/views/summary/index.tsx\n\n\n\n\n\n\n\n\n\nvar summary_Visible = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Visible, summary_Currency = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Currency;\nvar DisabledContinue = function () {\n    var page = (external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.getPageRoute() || \"\").replace(\"/\", \"\").toUpperCase();\n    var messageid = \"TOOLTIP_\".concat(page);\n    var _a = __read(external_root_React_commonjs2_react_commonjs_react_amd_react_.useState(false), 2), hover = _a[0], toggleHover = _a[1];\n    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"button\", { id: \"tierContinue\", onClick: function () { return toggleHover(!hover); }, onKeyUp: function () { return toggleHover(!hover); }, onMouseOver: function () { return toggleHover(true); }, onMouseOut: function () { return toggleHover(false); }, className: \"btn btn-primary fill-xs tooltip-interactive relative alignIconWithText disabled\", \"aria-disabled\": \"true\" },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Continue\" }),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(summary_Visible, { when: hover },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: messageid }, function (txt) { return Boolean(txt) && txt !== messageid ?\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"tooltip fade bs-tooltip-top show\", role: \"tooltip\", id: \"tooltip504192\", style: { position: \"absolute\", width: \"240px\", top: \"-110px\", left: \"50%\", transform: \"translateX(-50%)\" } },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"arrow\", style: { left: \"50%\" } }),\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"tooltip-inner\", style: { width: \"100%\" } },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexRow bgWhite txtBlack\" },\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"olt-icon icon-warning txtSize22 margin-10-right\" },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"volt-icon path1 yellowIcon\" }),\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"volt-icon path2\" })),\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"margin-5-top\" },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: messageid }))))) : external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", null); })));\n};\nvar PriceBlock = function (_a) {\n    var label = _a.label, price = _a.price, regularPrice = _a.regularPrice, _b = _a.className, className = _b === void 0 ? \"\" : _b;\n    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"virgin-dockbar-row flexStatic flexJustifyBetween-xs flexJustify \" + className },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"p\", { className: \"noMargin txtSize12 txtWhite\" }, label),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", null,\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(summary_Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(regularPrice, undefined, false) },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"p\", { className: \"noMargin txtSize12 txtWhite\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Price after credit\" }))),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"virginUltraReg txtCurrency txtSize28 pad-5-top txtWhite\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(summary_Currency, { value: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(price, \"price\", 0), monthly: true, prefixClassName: \"txtSize16 txtUppercase\", fractionClassName: \"txtSize16\" })),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(summary_Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(regularPrice, undefined, false) },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"p\", { className: \"noMargin txtSize12 txtWhite\" },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Current price\", values: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(regularPrice, undefined) })))));\n};\nvar summary_Component = function (_a) {\n    var summary = _a.summary, flowType = _a.flowType, location = _a.location, isContinueEnabled = _a.isContinueEnabled, onSummaryClick = _a.onSummaryClick, onCancelClick = _a.onCancelClick, onContinueClick = _a.onContinueClick, onCategoriesClick = _a.onCategoriesClick, handleNav = _a.handleNav;\n    var isTVStep = window.location.href.indexOf(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV) > -1;\n    var isVisible = !(location.pathname.indexOf(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.REVIEW) > 0 ||\n        location.pathname.indexOf(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.CONFIRMATION) > 0);\n    var tvSummaryContainer = document.getElementById(\"tv-sedebar-summary-portal\");\n    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(summary_Visible, { when: isVisible },\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"nav\", null,\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"virgin-dockbar col1 scrollTop\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"nopad bgBlack accss-focus-outline-override-black-bg\", style: { opacity: \"92%\" } },\n                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"virgin-dockbar-panel flexRow block-xs container container-fluid no-pad-xs\" },\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexRow block-xs\" },\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(summary_Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"Internet\", false) },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(summary_Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"?Internet.currentPrice\") },\n                                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(PriceBlock, { label: external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Current\" }), price: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"?Internet.currentPrice\"), regularPrice: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"?Internet.regularCurrentPrice\") })),\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(summary_Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"?Internet.newPrice\") },\n                                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(PriceBlock, { label: external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"NewInternet\" }), className: \"bgOrange\", price: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"?Internet.newPrice\"), regularPrice: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"?Internet.regularNewPrice\") }))),\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(summary_Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"TV\", false) },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(summary_Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"?TV.currentPrice\") },\n                                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(PriceBlock, { label: external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"CurrentTV\" }), price: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"?TV.currentPrice\"), regularPrice: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"?TV.regularCurrentPrice\") })),\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(summary_Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"?TV.newPrice\") },\n                                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(PriceBlock, { label: external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"NewTV\" }), className: \"bgOrange\", price: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"?TV.newPrice\"), regularPrice: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"?TV.regularNewPrice\") })))),\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(summary_Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"summaryAction\") },\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"virgin-dockbar-row flexCol-xs preview-btn\" },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"button\", { id: \"orderReview\", onClick: function () { return onSummaryClick(\"orderReview\"); }, className: \"btn btn-link txtUnderline txtWhite txtSize12 pad-10-left accss-changeplan-preview\" },\n                                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Preview\" })))),\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(summary_Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"resetAction\") },\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexStatic flexCol-xs preview-btn\" },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"button\", { id: \"orderCancel\", onClick: function () { return onCancelClick(\"orderCancel\"); }, className: \"btn btn-link txtWhite txtUnderline txtSize12 dockbar-cancel\" },\n                                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Reset\" })))),\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer10 visible-m\", \"aria-hidden\": \"true\" }),\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flexGrow\" }),\n                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"flex dockbar-buttons continue-button fullWidth-xs align-items-center flexCol-xs bgBlack\" },\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(summary_Visible, { when: isTVStep },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"virgin-dockbar-row flexStatic align-items-xs fill-xs no-pad-top-xs d-md-none\" },\n                                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"button\", { id: \"TV_CATEGORIES\", className: \"btn btn-secondary-inverted p-2\", onClick: handleNav },\n                                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"TV_CATEGORIES\" })))),\n                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"virgin-dockbar-row flexStatic align-items-xs fill-xs no-pad-top-xs\" },\n                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(summary_Visible, { when: isContinueEnabled, placeholder: external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(DisabledContinue, null) },\n                                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"button\", { onClick: onContinueClick, id: \"tier_Continue\", className: \"btn btn-primary fill-xs tooltip-interactive alignIconWithText pointer relative p-2\" },\n                                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Continue\" }),\n                                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(summary_Visible, { when: isTVStep && (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"productOfferingCount\", false) },\n                                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", { className: \"bgWhite txtSize12 pointer sans-serif txtBold txtGray txtCenter dockbar-notification\" },\n                                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"span\", null, (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"productOfferingCount\", 0))))))))))),\n            tvSummaryContainer &&\n                external_root_ReactDOM_commonjs2_react_dom_commonjs_react_dom_amd_react_dom_.createPortal(external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(TvSummaryPortal, { summary: summary, isContinueEnabled: isContinueEnabled, onContinueClick: onContinueClick }), tvSummaryContainer)));\n};\nvar SummaryPanel = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.connect)(function (_a) {\n    var summary = _a.summary;\n    return ({\n        summary: summary,\n        isContinueEnabled: !!(0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(summary, \"nextAction\", false)\n    });\n}, function (dispatch) { return ({\n    onSummaryClick: function (id) { return dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.openLightbox({ lightboxId: Summary_ModalId, data: { relativeId: id, lightbox: Summary_ModalId } })); },\n    onCancelClick: function (id) { return dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.openLightbox({ lightboxId: ApplicationReset_ModalId, data: { relativeId: id } })); },\n    onCategoriesClick: function () { return dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.broadcastUpdate(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.toggleTVCategoriesTray())); },\n    onContinueClick: function () { return dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.broadcastUpdate(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.onContinue())); },\n    handleNav: function () { return dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.broadcastUpdate(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.handleNav(true))); }\n}); })(summary_Component);\n\n;// ../src/views/index.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar RestrictionModal = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.RestrictionModal;\nvar errorOccured = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.errorOccured, widgetRenderComplete = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.widgetRenderComplete;\nvar AppRouter = function (props) {\n    var location = (0,external_root_ReactRouterDOM_commonjs2_react_router_dom_commonjs_react_router_dom_amd_react_router_dom_.useLocation)();\n    var environmentVariables = external_root_React_commonjs2_react_commonjs_react_amd_react_.useContext(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.WidgetContext).config.environmentVariables;\n    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_React_commonjs2_react_commonjs_react_amd_react_.Fragment, null,\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"style\", null, \"\\n                .brf .modal.fade.do-not-center-in .modal-dialog {\\n                    transform: none!important;\\n                    top: auto;\\n                }\\n                .brf .modal.fade.do-not-center-in .modal-content {\\n                    min-height: 460px;\\n                }\\n            \"),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Header, __assign({}, props, { location: location })),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactRouterDOM_commonjs2_react_router_dom_commonjs_react_router_dom_amd_react_router_dom_.Route, { path: \"*\", component: function (_a) {\n                var history = _a.history;\n                setHistoryProvider(history);\n                return null;\n            } }),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactRouterDOM_commonjs2_react_router_dom_commonjs_react_router_dom_amd_react_router_dom_.Switch, null,\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactRouterDOM_commonjs2_react_router_dom_commonjs_react_router_dom_amd_react_router_dom_.Route, { path: [\n                    \"/Changepackage/Internet/Appointment\",\n                    \"/Bundle/Internet/Appointment\"\n                ] },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Appointment, { title: \"Appointment\" })),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactRouterDOM_commonjs2_react_router_dom_commonjs_react_router_dom_amd_react_router_dom_.Route, { path: [\n                    \"/Changepackage/Internet/Review\",\n                    \"/Changepackage/TV/Review\",\n                    \"/Add/TV/Review\",\n                    \"/Bundle/Review\"\n                ] },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Review, { title: \"Review\" })),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactRouterDOM_commonjs2_react_router_dom_commonjs_react_router_dom_amd_react_router_dom_.Route, { path: [\n                    \"/Changepackage/Internet/Confirmation\",\n                    \"/Changepackage/TV/Confirmation\",\n                    \"/Add/TV/Confirmation\",\n                    \"/Bundle/Confirmation\"\n                ] },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Confirmation, { title: \"Confirmation\" })),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactRouterDOM_commonjs2_react_router_dom_commonjs_react_router_dom_amd_react_router_dom_.Route, { path: [\n                    \"/Changepackage/Internet\",\n                    \"/Bundle/Internet\"\n                ] },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Internet, { title: \"Internet\" })),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactRouterDOM_commonjs2_react_router_dom_commonjs_react_router_dom_amd_react_router_dom_.Route, { path: [\n                    \"/Changepackage/TV\",\n                    \"/Bundle/TV\",\n                    \"/Add/TV\"\n                ] },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(TV, { title: environmentVariables.language === \"fr\" ? \"Configurez vos service - TV\" : \"Set up your service - TV\" })),\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactRouterDOM_commonjs2_react_router_dom_commonjs_react_router_dom_amd_react_router_dom_.Route, { path: \"*\" },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactRouterDOM_commonjs2_react_router_dom_commonjs_react_router_dom_amd_react_router_dom_.Redirect, { to: props.defaultRoute }))),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(SummaryPanel, __assign({}, props, { location: location })),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Footer, null),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(PreviewLightbox, null),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(RestrictionModal, { id: \"NAVIGATION_RESTRICTION_MODAL\" }),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(ApplicationResetLightbox, null),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(ApplicationExitLightbox, null),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(ApplicationLogoutLightbox, null),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer20 hidden-xs hidden-m\", \"aria-hidden\": \"true\" }),\n        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(\"div\", { className: \"spacer60 hidden-xs hidden-m\", \"aria-hidden\": \"true\" }));\n};\nvar views_Component = (function (_super) {\n    __extends(Component, _super);\n    function Component() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Component.prototype.componentDidCatch = function (err) {\n        this.props.onErrorEncountered(err);\n    };\n    Component.prototype.componentDidMount = function () {\n        this.props.widgetRenderComplete(\"omf-changepackage-navigation\");\n    };\n    Component.prototype.render = function () {\n        return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactRouterDOM_commonjs2_react_router_dom_commonjs_react_router_dom_amd_react_router_dom_.BrowserRouter, { basename: \"/Ordering\" },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(AppRouter, __assign({}, this.props))));\n    };\n    return Component;\n}(external_root_React_commonjs2_react_commonjs_react_amd_react_.Component));\nvar Application = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.connect)(function (_a) {\n    var defaultRoute = _a.defaultRoute, flowType = _a.flowType;\n    return ({ defaultRoute: defaultRoute, flowType: flowType });\n}, function (dispatch) { return ({\n    onErrorEncountered: function (error) { return dispatch(errorOccured(error)); },\n    widgetRenderComplete: function () { return dispatch(widgetRenderComplete()); }\n}); })(views_Component);\n\n;// ../src/App.tsx\n\n\n\nvar ApplicationRoot = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.ApplicationRoot;\nvar App = function () { return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(ApplicationRoot, null,\n    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Application, null)); };\n\n;// ../src/Config.ts\n\n\n\nvar BaseConfig = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.BaseConfig, configProperty = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.configProperty;\nvar Config = (function (_super) {\n    __extends(Config, _super);\n    function Config() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    __decorate([\n        configProperty(\"\"),\n        __metadata(\"design:type\", String)\n    ], Config.prototype, \"flowType\", void 0);\n    __decorate([\n        configProperty({}),\n        __metadata(\"design:type\", Object)\n    ], Config.prototype, \"environmentVariables\", void 0);\n    __decorate([\n        configProperty({}),\n        __metadata(\"design:type\", Object)\n    ], Config.prototype, \"mockdata\", void 0);\n    __decorate([\n        configProperty({}),\n        __metadata(\"design:type\", Object)\n    ], Config.prototype, \"headers\", void 0);\n    __decorate([\n        configProperty({ base: \"http://127.0.0.1:8881\" }),\n        __metadata(\"design:type\", Object)\n    ], Config.prototype, \"api\", void 0);\n    __decorate([\n        configProperty(\"\"),\n        __metadata(\"design:type\", String)\n    ], Config.prototype, \"defaultRoute\", void 0);\n    __decorate([\n        configProperty({}),\n        __metadata(\"design:type\", Object)\n    ], Config.prototype, \"linkURL\", void 0);\n    Config = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable\n    ], Config);\n    return Config;\n}(BaseConfig));\n\n\n// EXTERNAL MODULE: external {\"root\":\"Redux\",\"commonjs2\":\"redux\",\"commonjs\":\"redux\",\"amd\":\"redux\"}\nvar external_root_Redux_commonjs2_redux_commonjs_redux_amd_redux_ = __webpack_require__(\"redux\");\n// EXTERNAL MODULE: external {\"root\":\"ReduxActions\",\"commonjs2\":\"redux-actions\",\"commonjs\":\"redux-actions\",\"amd\":\"redux-actions\"}\nvar external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_ = __webpack_require__(\"redux-actions\");\n// EXTERNAL MODULE: external {\"root\":\"ReduxObservable\",\"commonjs2\":\"redux-observable\",\"commonjs\":\"redux-observable\",\"amd\":\"redux-observable\"}\nvar external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_ = __webpack_require__(\"redux-observable\");\n;// ../src/mutators/index.ts\n\n\nfunction summaryTransformerFn(data) {\n    var priceOvertime = (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(data, \"priceOvertime\", []);\n    var summary = __assign(__assign({}, data), { Internet: priceOvertime.find(function (price) { return price.flowType === \"Internet\"; }), TV: priceOvertime.find(function (price) { return price.flowType === \"TV\"; }) });\n    return summary;\n}\nfunction setFlowTypeFn(type) {\n    sessionStorage.setItem(\"omf:Flowtype\", type);\n    switch (type) {\n        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.INTERNET:\n            external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture().updateContext({\n                s_oSS2: \"Internet\"\n            });\n            break;\n        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.TV:\n            external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture().updateContext({\n                s_oSS2: \"Change package\"\n            });\n            break;\n        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.ADDTV:\n            external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture().updateContext({\n                s_oSS2: \"Add TV\"\n            });\n            break;\n        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.BUNDLE:\n            external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture().updateContext({\n                s_oSS2: \"Bundle\"\n            });\n            break;\n    }\n    return type;\n}\n\n;// ../src/store/Actions.ts\n\n\nvar setFlowType = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"SET_FLOW_TYPE\", setFlowTypeFn);\nvar setSummaryTotals = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"SET_FLOW_SUMMARY_TOTALS\", summaryTransformerFn);\nvar checkRestrictions = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"CHECK_NAVIGATION_RESTRICTIONS\");\n\n;// ../src/Client.ts\n\n\n\n\nvar Client = (function (_super) {\n    __extends(Client, _super);\n    function Client(ajaxClient, config) {\n        return _super.call(this, ajaxClient, config) || this;\n    }\n    Client = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable,\n        __metadata(\"design:paramtypes\", [external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.AjaxServices, Config])\n    ], Client);\n    return Client;\n}(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.BaseClient));\n\n\n// EXTERNAL MODULE: external {\"root\":\"rxjs\",\"commonjs2\":\"rxjs\",\"commonjs\":\"rxjs\",\"amd\":\"rxjs\"}\nvar external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_ = __webpack_require__(\"rxjs\");\n;// ../src/store/Epics/Navigation.ts\n\n\n\n\n\n\n\n\n\n\nvar showHideLoader = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.showHideLoader, continueFlow = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.continueFlow, historyBack = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.historyBack, historyForward = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.historyForward, historyGo = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.historyGo, openLightbox = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.openLightbox, applicationExit = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.applicationExit, applicationLogout = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.applicationLogout, setWidgetStatus = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setWidgetStatus, applicationReset = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.applicationReset, closeLightbox = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.closeLightbox;\nfunction getBaseRoute(route) {\n    return (route || \"\").replace(/\\/Ordering|\\/Packages|\\/Movies|\\/Addons|\\/Alacarte|\\/International|\\/Combos|\\/Browse|\\/Search/i, \"\");\n}\nfunction getCurrentRoute() {\n    var history = useHistory();\n    return getBaseRoute(history.location.pathname);\n}\nvar NavigationEpics = (function () {\n    function NavigationEpics(client, config) {\n        this.client = client;\n        this.config = config;\n    }\n    NavigationEpics.prototype.combineEpics = function () {\n        return (0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.combineEpics)(this.historyGoEpic, this.historyForwardEpic, this.historyBackEpic, this.applicationExitEpic, this.applicationLogoutEpic, this.checkRestrictionsEpic, this.applicationResetEpic);\n    };\n    Object.defineProperty(NavigationEpics.prototype, \"checkRestrictionsEpic\", {\n        get: function () {\n            var _this = this;\n            return function (action$, state$) {\n                return action$.pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (action) { return action.type === checkRestrictions.toString(); }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (_a) {\n                    var payload = _a.payload;\n                    return Boolean(payload);\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function (_a) {\n                    var payload = _a.payload;\n                    return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.concat)([setWidgetStatus(_this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.UPDATING)], _this.client.action(payload).pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function (response) { return (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FilterRestrictionObservable)(response, [\n                        historyGo(response.data.redirectURLKey)\n                    ]); })));\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.catchError)(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Models.ErrorHandlerObservable(checkRestrictions)));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(NavigationEpics.prototype, \"historyGoEpic\", {\n        get: function () {\n            return function (action$, state$) {\n                return action$.pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (action) { return action.type === historyGo.toString(); }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (_a) {\n                    var payload = _a.payload;\n                    return (typeof payload === \"string\");\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function (_a) {\n                    var payload = _a.payload;\n                    var flowType = state$.value.flowType;\n                    var destination = payload;\n                    var destinationRoute = \"\";\n                    switch (payload) {\n                        case \"APPOINTMENT\":\n                        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.APPOINTMENT:\n                            destination = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.constructPageRoute(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.APPOINTMENT);\n                            destinationRoute = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.APPOINTMENT;\n                            break;\n                        case \"INTERNET_REVIEW\":\n                        case \"TV_REVIEW\":\n                        case \"BUNDLE_REVIEW\":\n                        case \"REVIEW\":\n                        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.REVIEW:\n                            destination = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.constructPageRoute(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.REVIEW);\n                            destinationRoute = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.REVIEW;\n                            break;\n                        case \"INTERNET_CONFIRMATION\":\n                        case \"TV_CONFIRMATION\":\n                        case \"BUNDLE_CONFIRMATION\":\n                        case \"CONFIRMATION\":\n                        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.CONFIRMATION:\n                            destination = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.constructPageRoute(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.CONFIRMATION);\n                            destinationRoute = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.CONFIRMATION;\n                            break;\n                        case \"ADD_TV_REVIEW\":\n                            destination = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.constructPageRoute(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.REVIEW, external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.ADDTV);\n                            destinationRoute = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.REVIEW;\n                            break;\n                        case \"INTERNET_CP\":\n                        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.INTERNET:\n                            destination = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.constructPageRoute(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.INTERNET);\n                            destinationRoute = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.INTERNET;\n                            break;\n                        case \"TV_CP\":\n                        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV:\n                            destination = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.constructPageRoute(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV);\n                            destinationRoute = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV;\n                            break;\n                        case \"ADD_TV\":\n                            destination = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.constructPageRoute(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV, external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.ADDTV);\n                            destinationRoute = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV;\n                            flowType = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.ADDTV;\n                            break;\n                        case \"BUNDLE_TV\":\n                            destination = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.constructPageRoute(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV, external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.BUNDLE);\n                            destinationRoute = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV;\n                            flowType = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.BUNDLE;\n                            break;\n                        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Volt.EDIsplayGroupKey.BASE_PROGRAMMING:\n                            destination = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.constructPageRoute(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV) + external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV_Packages;\n                            destinationRoute = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV;\n                            break;\n                        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Volt.EDIsplayGroupKey.ADD_ON:\n                            destination = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.constructPageRoute(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV) + external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV_Addons;\n                            destinationRoute = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV;\n                            break;\n                        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Volt.EDIsplayGroupKey.ALACARTE:\n                            destination = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.constructPageRoute(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV) + external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV_Alacarte;\n                            destinationRoute = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV;\n                            break;\n                        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Volt.EDIsplayGroupKey.MOVIE:\n                            destination = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.constructPageRoute(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV) + external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV_MoviesSeries;\n                            destinationRoute = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV;\n                            break;\n                        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Volt.EDIsplayGroupKey.INTERNATIONAL:\n                        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Volt.EDIsplayGroupKey.INTERNATIONAL_COMBOS:\n                            destination = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.constructPageRoute(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV) + external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV_InternationalCombos;\n                            destinationRoute = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV;\n                            break;\n                        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Volt.EDIsplayGroupKey.INTERNATIONAL_ALACARTE:\n                            destination = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.constructPageRoute(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV) + external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV_InternationalAlacarte;\n                            destinationRoute = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV;\n                            break;\n                        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Volt.EDIsplayGroupKey.TV_BROWSE_ALL:\n                            destination = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.constructPageRoute(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV) + external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV_Browse;\n                            destinationRoute = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV;\n                            break;\n                        case \"TV_SEARCH\":\n                            destination = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.constructPageRoute(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV) + external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV_Search;\n                            destinationRoute = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV;\n                            break;\n                        case \"INTERNET_OVERVIEW\":\n                        case \"TV_OVERVIEW\":\n                            return [\n                                applicationExit()\n                            ];\n                    }\n                    if (external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.getPageRoute() === external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV && destinationRoute === external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.TV) {\n                        var history_1 = useTVHistory();\n                        window.requestAnimationFrame(function () { return history_1.push(destination.replace(/\\/Ordering|\\/Changepackage|\\/TV|\\/Add\\b|\\/Bundle/gi, \"\")); });\n                        return [\n                            showHideLoader(null)\n                        ];\n                    }\n                    else if (external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.getPageRoute() === external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.INTERNET && destinationRoute === external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetRoute.INTERNET) {\n                        return [\n                            showHideLoader(null)\n                        ];\n                    }\n                    else {\n                        var history_2 = useHistory();\n                        window.requestAnimationFrame(function () { return history_2.push(destination); });\n                        return ([\n                            setFlowType(flowType)\n                        ]);\n                    }\n                }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(NavigationEpics.prototype, \"historyForwardEpic\", {\n        get: function () {\n            return function (action$, state$) {\n                return action$.pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (action) {\n                    return action.type === historyForward.toString() ||\n                        action.type === continueFlow.toString();\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () {\n                    var history = useHistory();\n                    var state = state$.value;\n                    var routes = state.routes;\n                    var nextAction = (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(state, \"summary.nextAction\", {});\n                    switch (true) {\n                        case Boolean(nextAction.href):\n                            return [\n                                checkRestrictions(nextAction)\n                            ];\n                        case Boolean(nextAction.redirectURLKey):\n                            return [\n                                historyGo(nextAction.redirectURLKey)\n                            ];\n                        default:\n                            var route_1 = history.location.pathname;\n                            var index = routes.findIndex(function (r) { return route_1.indexOf(r) > -1; });\n                            if (index === routes.length - 1) {\n                                return [];\n                            }\n                            index += 1;\n                            route_1 = routes[index];\n                            history.push(route_1);\n                            return [];\n                    }\n                }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(NavigationEpics.prototype, \"historyBackEpic\", {\n        get: function () {\n            return function (action$, state$) {\n                return action$.pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (action) { return action.type === historyBack.toString(); }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function (_a) {\n                    var payload = _a.payload;\n                    var history = useHistory();\n                    var state = state$.value;\n                    var routes = state.routes;\n                    var backAction = (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(state, \"summary.backAction\", {});\n                    switch (true) {\n                        case Boolean(backAction.href):\n                            return [\n                                checkRestrictions(backAction)\n                            ];\n                        case Boolean(backAction.redirectURLKey):\n                            if (backAction.redirectURLKey === \"INTERNET_OVERVIEW\" ||\n                                backAction.redirectURLKey === \"TV_OVERVIEW\") {\n                                return [\n                                    showHideLoader(false),\n                                    openLightbox({ lightboxId: ApplicationExit_ModalId, data: { relativeId: payload } })\n                                ];\n                            }\n                            else {\n                                return [\n                                    historyGo(backAction.redirectURLKey)\n                                ];\n                            }\n                        default:\n                            var route_2 = getCurrentRoute();\n                            var index = routes.findIndex(function (r) { return route_2 === r; });\n                            if (index === 0) {\n                                return [\n                                    showHideLoader(false),\n                                    openLightbox({ lightboxId: ApplicationExit_ModalId, data: { relativeId: payload } })\n                                ];\n                            }\n                            index -= 1;\n                            if (route_2) {\n                                route_2 = routes[index];\n                                if (route_2.indexOf(\"Appointment\") > -1 && !sessionStorage.getItem(\"omf:hasAppointmentRoute\")) {\n                                    index -= 1;\n                                    route_2 = routes[index];\n                                }\n                                history.push(route_2);\n                            }\n                            return [];\n                    }\n                }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(NavigationEpics.prototype, \"applicationExitEpic\", {\n        get: function () {\n            var _this = this;\n            return function (action$, state$) {\n                return action$.pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (action) { return action.type === applicationExit.toString(); }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () {\n                    sessionStorage.clear();\n                    window.location = (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(_this.config, \"linkURL.exitURL\", \"\");\n                    return [\n                        showHideLoader(true)\n                    ];\n                }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(NavigationEpics.prototype, \"applicationResetEpic\", {\n        get: function () {\n            var _this = this;\n            return function (action$, state$) {\n                return action$.pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (action) { return action.type === applicationReset.toString(); }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () {\n                    var state = state$.value;\n                    var nextAction = (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(state, \"summary.resetAction\", {});\n                    return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.concat)([showHideLoader(true)], _this.client.post(nextAction.href, nextAction.messageBody).pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function (resp) { return [\n                        closeLightbox(\"APPLICATION_RESET\"),\n                        window.location.reload()\n                    ]; })));\n                }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(NavigationEpics.prototype, \"applicationLogoutEpic\", {\n        get: function () {\n            var _this = this;\n            return function (action$, state$) {\n                return action$.pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (action) { return action.type === applicationLogout.toString(); }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () {\n                    sessionStorage.clear();\n                    window.location = (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(_this.config, \"linkURL.logoutURL\", \"\");\n                    return [\n                        showHideLoader(true)\n                    ];\n                }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    NavigationEpics = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable,\n        __metadata(\"design:paramtypes\", [Client, Config])\n    ], NavigationEpics);\n    return NavigationEpics;\n}());\n\n\n;// ../src/store/Epics.ts\n\n\n\n\n\n\n\nvar Epics_setWidgetStatus = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setWidgetStatus;\nvar Epics = (function () {\n    function Epics(navigationEpics) {\n        this.navigationEpics = navigationEpics;\n    }\n    Epics.prototype.combineEpics = function () {\n        return (0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.combineEpics)(this.onWidgetStatusEpic);\n    };\n    Object.defineProperty(Epics.prototype, \"onWidgetStatusEpic\", {\n        get: function () {\n            return function (action$) {\n                return action$.pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (action) { return action.type === Epics_setWidgetStatus.toString(); }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (_a) {\n                    var payload = _a.payload;\n                    return payload === external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.INIT;\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () { return [\n                    setFlowType(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.getFlowType()),\n                    Epics_setWidgetStatus(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.RENDERED)\n                ]; }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Epics = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable,\n        __metadata(\"design:paramtypes\", [NavigationEpics])\n    ], Epics);\n    return Epics;\n}());\n\n\n;// ../src/store/Store.ts\n\n\n\n\n\n\n\n\n\n\n\nvar BaseStore = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.BaseStore, actionsToComputedPropertyName = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.actionsToComputedPropertyName;\nvar setWidgetProps = actionsToComputedPropertyName(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions).setWidgetProps;\nvar Store_a = actionsToComputedPropertyName(Actions_namespaceObject), Store_setFlowType = Store_a.setFlowType, Store_setSummaryTotals = Store_a.setSummaryTotals;\nvar Store = (function (_super) {\n    __extends(Store, _super);\n    function Store(client, store, epics, localization) {\n        var _this = _super.call(this, store) || this;\n        _this.client = client;\n        _this.epics = epics;\n        _this.localization = localization;\n        return _this;\n    }\n    Object.defineProperty(Store.prototype, \"reducer\", {\n        get: function () {\n            var _a, _b, _c, _d;\n            return (0,external_root_Redux_commonjs2_redux_commonjs_redux_amd_redux_.combineReducers)(__assign(__assign(__assign(__assign({}, external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Reducers.WidgetBaseLifecycle(this.localization)), external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Reducers.WidgetLightboxes()), external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Reducers.WidgetRestrictions()), { defaultRoute: (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.handleActions)((_a = {},\n                    _a[setWidgetProps] = function (state, _a) {\n                        var payload = _a.payload;\n                        return (payload && payload.defaultRoute) || state;\n                    },\n                    _a), \"/\"), flowType: (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.handleActions)((_b = {},\n                    _b[setWidgetProps] = function (state, _a) {\n                        var payload = _a.payload;\n                        return (payload && payload.flowType) || state;\n                    },\n                    _b[Store_setFlowType] = function (state, _a) {\n                        var payload = _a.payload;\n                        return (payload && payload) || state;\n                    },\n                    _b), \"\"), routes: (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.handleActions)((_c = {},\n                    _c[setWidgetProps] = function (state, _a) {\n                        var payload = _a.payload;\n                        return (payload && payload.flowType && Routes[payload.flowType]) || state;\n                    },\n                    _c[Store_setFlowType] = function (state, _a) {\n                        var payload = _a.payload;\n                        return (payload && Routes[payload]) || state;\n                    },\n                    _c), []), summary: (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.handleActions)((_d = {},\n                    _d[Store_setSummaryTotals] = function (state, _a) {\n                        var payload = _a.payload;\n                        return (payload && payload) || state;\n                    },\n                    _d), {}) }));\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Store.prototype, \"middlewares\", {\n        get: function () {\n            return (0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.combineEpics)(this.epics.navigationEpics.combineEpics(), this.epics.combineEpics(), new external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ModalEpics().combineEpics(), new external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.RestricitonsEpics(this.client, \"NAVIGATION_RESTRICTION_MODAL\").combineEpics(), new external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.LifecycleEpics().combineEpics());\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Store = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable,\n        __metadata(\"design:paramtypes\", [Client, external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Store, Epics, Localization])\n    ], Store);\n    return Store;\n}(BaseStore));\n\n\n;// ../src/store/index.ts\n\n\n\n;// ../src/Pipe.ts\n\n\n\n\n\nvar BasePipe = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.BasePipe;\nvar Pipe = (function (_super) {\n    __extends(Pipe, _super);\n    function Pipe(arg) {\n        var _this = _super.call(this, arg) || this;\n        Pipe.instance = _this;\n        return _this;\n    }\n    Pipe.Subscriptions = function (store) {\n        var _a;\n        return _a = {},\n            _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.historyGo.toString()] = function (_a) {\n                var payload = _a.payload, meta = _a.meta;\n                store.dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.historyGo(payload));\n            },\n            _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.historyBack.toString()] = function () {\n                store.dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.historyBack());\n            },\n            _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.historyForward.toString()] = function () {\n                store.dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.historyForward());\n            },\n            _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.applicationExit.toString()] = function () {\n                store.dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.applicationExit());\n            },\n            _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.applicationLogout.toString()] = function () {\n                store.dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.applicationLogout());\n            },\n            _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.refreshTotals.toString()] = function () {\n                store.dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.refreshTotals());\n            },\n            _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setProductConfigurationTotal.toString()] = function (_a) {\n                var payload = _a.payload;\n                store.dispatch(setSummaryTotals(payload));\n            },\n            _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.closeLightbox.toString()] = function (_a) {\n                var payload = _a.payload;\n                store.dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.closeLightbox(payload));\n            },\n            _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setHistoryProvider.toString()] = function (_a) {\n                var payload = _a.payload;\n                setTVHistoryProvider(payload);\n            },\n            _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setAppointmentVisited.toString()] = function () {\n                setAppointmentVisited();\n            },\n            _a;\n    };\n    return Pipe;\n}(BasePipe));\n\n\n;// ../src/Widget.tsx\n\n\n\n\n\n\n\n\n\nvar Widget_setWidgetProps = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setWidgetProps, Widget_setWidgetStatus = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setWidgetStatus;\nvar WidgetContainer = (function (_super) {\n    __extends(WidgetContainer, _super);\n    function WidgetContainer(store, params, config, pipe) {\n        var _this = _super.call(this) || this;\n        _this.store = store;\n        _this.params = params;\n        _this.config = config;\n        _this.pipe = pipe;\n        return _this;\n    }\n    WidgetContainer.prototype.init = function () {\n        this.pipe.subscribe(Pipe.Subscriptions(this.store));\n        this.store.dispatch(Widget_setWidgetProps(this.config));\n        this.store.dispatch(Widget_setWidgetProps(this.params.props));\n        this.store.dispatch(Widget_setWidgetStatus(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.INIT));\n    };\n    WidgetContainer.prototype.destroy = function () {\n        this.pipe.unsubscribe();\n        this.store.destroy();\n    };\n    WidgetContainer.prototype.render = function (root) {\n        var store = this.store;\n        var path = window.location.pathname.split(\"/\");\n        root.render(external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ContextProvider, { value: { config: this.config, mode: \"/\".concat(path[path.length - 1]) } },\n            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.Provider, { store: store },\n                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(App, null))));\n    };\n    WidgetContainer = __decorate([\n        (0,external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Widget)({ namespace: \"Ordering\" }),\n        __metadata(\"design:paramtypes\", [Store, external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.ParamsProvider, Config, Pipe])\n    ], WidgetContainer);\n    return WidgetContainer;\n}(external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.ViewWidget));\n/* harmony default export */ var Widget = (WidgetContainer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../src/Widget.tsx\n\n}");

/***/ }),

/***/ "bwtk":
/*!**********************************************************************************!*\
  !*** external {"root":"bwtk","commonjs2":"bwtk","commonjs":"bwtk","amd":"bwtk"} ***!
  \**********************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_bwtk__;

/***/ }),

/***/ "omf-changepackage-components":
/*!********************************************************************************************************************************************************************************!*\
  !*** external {"root":"OMFChangepackageComponents","commonjs2":"omf-changepackage-components","commonjs":"omf-changepackage-components","amd":"omf-changepackage-components"} ***!
  \********************************************************************************************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_omf_changepackage_components__;

/***/ }),

/***/ "react":
/*!**************************************************************************************!*\
  !*** external {"root":"React","commonjs2":"react","commonjs":"react","amd":"react"} ***!
  \**************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

/***/ }),

/***/ "react-dom":
/*!*****************************************************************************************************!*\
  !*** external {"root":"ReactDOM","commonjs2":"react-dom","commonjs":"react-dom","amd":"react-dom"} ***!
  \*****************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_react_dom__;

/***/ }),

/***/ "react-intl":
/*!*********************************************************************************************************!*\
  !*** external {"root":"ReactIntl","commonjs2":"react-intl","commonjs":"react-intl","amd":"react-intl"} ***!
  \*********************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_react_intl__;

/***/ }),

/***/ "react-redux":
/*!*************************************************************************************************************!*\
  !*** external {"root":"ReactRedux","commonjs2":"react-redux","commonjs":"react-redux","amd":"react-redux"} ***!
  \*************************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_react_redux__;

/***/ }),

/***/ "react-router-dom":
/*!********************************************************************************************************************************!*\
  !*** external {"root":"ReactRouterDOM","commonjs2":"react-router-dom","commonjs":"react-router-dom","amd":"react-router-dom"} ***!
  \********************************************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_react_router_dom__;

/***/ }),

/***/ "redux":
/*!**************************************************************************************!*\
  !*** external {"root":"Redux","commonjs2":"redux","commonjs":"redux","amd":"redux"} ***!
  \**************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_redux__;

/***/ }),

/***/ "redux-actions":
/*!*********************************************************************************************************************!*\
  !*** external {"root":"ReduxActions","commonjs2":"redux-actions","commonjs":"redux-actions","amd":"redux-actions"} ***!
  \*********************************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_redux_actions__;

/***/ }),

/***/ "redux-observable":
/*!*********************************************************************************************************************************!*\
  !*** external {"root":"ReduxObservable","commonjs2":"redux-observable","commonjs":"redux-observable","amd":"redux-observable"} ***!
  \*********************************************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_redux_observable__;

/***/ }),

/***/ "rxjs":
/*!**********************************************************************************!*\
  !*** external {"root":"rxjs","commonjs2":"rxjs","commonjs":"rxjs","amd":"rxjs"} ***!
  \**********************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_rxjs__;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	!function() {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = function(exports, definition) {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	!function() {
/******/ 		__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	!function() {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = function(exports) {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	}();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval-source-map devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("../src/Widget.tsx");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});