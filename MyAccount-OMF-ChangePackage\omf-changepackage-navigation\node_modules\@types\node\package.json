{"name": "@types/node", "version": "13.13.4", "description": "TypeScript definitions for Node.js", "license": "MIT", "contributors": [{"name": "Microsoft TypeScript", "url": "https://github.com/Microsoft", "githubUsername": "Microsoft"}, {"name": "DefinitelyTyped", "url": "https://github.com/DefinitelyTyped", "githubUsername": "DefinitelyTyped"}, {"name": "<PERSON>", "url": "https://github.com/jkomyno", "githubUsername": "jkomyno"}, {"name": "<PERSON>.", "url": "https://github.com/a-tarasyuk", "githubUsername": "a-tarasyuk"}, {"name": "Alvis <PERSON>", "url": "https://github.com/alvis", "githubUsername": "alvis"}, {"name": "<PERSON>", "url": "https://github.com/r3nya", "githubUsername": "r3nya"}, {"name": "<PERSON>", "url": "https://github.com/btoueg", "githubUsername": "btoueg"}, {"name": "<PERSON>", "url": "https://github.com/bruno<PERSON>ufler", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Chigozirim C.", "url": "https://github.com/smac89", "githubUsername": "smac89"}, {"name": "<PERSON>", "url": "https://github.com/tellnes", "githubUsername": "tellnes"}, {"name": "<PERSON>", "url": "https://github.com/touffy", "githubUsername": "touffy"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/DeividasBakanas", "githubUsername": "DeividasBakanas"}, {"name": "<PERSON>", "url": "https://github.com/eyqs", "githubUsername": "eyqs"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Flarna", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>-<PERSON>-CK", "githubUsername": "Hannes-<PERSON><PERSON>-CK"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/KSXGitHub", "githubUsername": "KSXGitHub"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/hoo29", "githubUsername": "hoo29"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kjin", "githubUsername": "kjin"}, {"name": "<PERSON>", "url": "https://github.com/ajafff", "githubUsername": "a<PERSON><PERSON><PERSON>"}, {"name": "Lishude", "url": "https://github.com/islishude", "githubUsername": "islishude"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mwiktorczyk", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/mohsen1", "githubUsername": "mohsen1"}, {"name": "<PERSON>", "url": "https://github.com/n-e", "githubUsername": "n-e"}, {"name": "<PERSON>", "url": "https://github.com/octo-sniffle", "githubUsername": "octo-sniffle"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/galkin", "githubUsername": "galkin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/parambirs", "githubUsername": "parambirs"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ThomasdenH", "githubUsername": "ThomasdenH"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/WilcoBakker", "githubUsername": "WilcoBakker"}, {"name": "wwwy3y3", "url": "https://github.com/wwwy3y3", "githubUsername": "wwwy3y3"}, {"name": "<PERSON>", "url": "https://github.com/samuela", "githubUsername": "samuela"}, {"name": "<PERSON>", "url": "https://github.com/kuehlein", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/j-oliveras", "githubUsername": "j-oliveras"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/bhongy", "githubUsername": "b<PERSON>y"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/chyzwar", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/trivikr", "githubUsername": "trivikr"}, {"name": "<PERSON>", "url": "https://github.com/nguymin4", "githubUsername": "nguymin4"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/yoursunny", "githubUsername": "<PERSON><PERSON>ny"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/qwelias", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "ExE Boss", "url": "https://github.com/ExE-Boss", "githubUsername": "ExE-Boss"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON>-<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "typesVersions": {">=3.2.0-0": {"*": ["ts3.2/*"]}, ">=3.5.0-0": {"*": ["ts3.5/*"]}, ">=3.7.0-0": {"*": ["ts3.7/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "57cfc32a2e6aa482bfd078aa0560ba17922bc022cb7b435dea5d619f183a1cb1", "typeScriptVersion": "2.8"}