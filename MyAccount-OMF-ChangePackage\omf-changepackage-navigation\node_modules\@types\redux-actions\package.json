{"name": "@types/redux-actions", "version": "2.6.2", "description": "TypeScript definitions for redux-actions", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/redux-actions", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/jaysoo", "githubUsername": "jaysoo"}, {"name": "<PERSON>", "url": "https://github.com/alex<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/alechill", "githubUsername": "alechill"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/alexey-pelykh", "githubUsername": "alexey-pelykh"}, {"name": "<PERSON><PERSON><PERSON> Andrade", "url": "https://github.com/7hi4g0", "githubUsername": "7hi4g0"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/oddui", "githubUsername": "oddui"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/redux-actions"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "c8140493914bd52fb23465f305c8611e764ebf456fa04cdaed348ef66cc8ea15", "typeScriptVersion": "3.6"}