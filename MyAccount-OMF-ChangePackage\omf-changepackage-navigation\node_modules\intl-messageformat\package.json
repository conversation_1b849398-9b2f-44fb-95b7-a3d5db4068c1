{"name": "intl-messageformat", "description": "Formats ICU Message strings with number, date, plural, and select placeholders to create localized messages.", "version": "10.7.16", "license": "BSD-3-<PERSON><PERSON>", "author": "<PERSON> <<EMAIL>>", "sideEffects": false, "types": "index.d.ts", "dependencies": {"tslib": "^2.8.0", "@formatjs/ecma402-abstract": "2.3.4", "@formatjs/icu-messageformat-parser": "2.11.2", "@formatjs/fast-memoize": "2.2.7"}, "bugs": "https://github.com/formatjs/formatjs/issues", "contributors": ["<PERSON> <<EMAIL>>", "Caridy <PERSON>ino <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "gitHead": "a7842673d8ad205171ad7c8cb8bb2f318b427c0c", "homepage": "https://github.com/formatjs/formatjs", "keywords": ["globalization", "i18n", "icu", "internationalization", "intl", "localization", "messageformat", "parser", "plural"], "main": "index.js", "module": "lib/index.js", "repository": "**************:formatjs/formatjs.git"}