{"name": "react-intl", "description": "Internationalize React apps. This library provides React components and an API to format dates, numbers, and strings, including pluralization and handling translations.", "version": "7.1.11", "license": "BSD-3-<PERSON><PERSON>", "author": "<PERSON> <<EMAIL>>", "sideEffects": false, "types": "index.d.ts", "dependencies": {"@types/hoist-non-react-statics": "^3.3.1", "@types/react": "16 || 17 || 18 || 19", "hoist-non-react-statics": "^3.3.2", "tslib": "^2.8.0", "intl-messageformat": "10.7.16", "@formatjs/icu-messageformat-parser": "2.11.2", "@formatjs/intl": "3.1.6", "@formatjs/ecma402-abstract": "2.3.4"}, "peerDependencies": {"react": "16 || 17 || 18 || 19", "typescript": "^5.6.0"}, "browserslist": ["ie 11"], "bugs": "https://github.com/formatjs/formatjs/issues", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Caridy <PERSON>ino <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <dj<PERSON><PERSON>@users.noreply.github.com>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "DragonRaider5 <<EMAIL>>", "dropfen <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <janic<PERSON>ples<PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <johanne<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON> <jose<PERSON><PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "p10ns11y <<EMAIL>>", "papasmile <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "PutziSan <<EMAIL>>", "Rifat Nabi <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "rsamec <<EMAIL>>", "R<PERSON>ya44 <rust<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "salagadoola <<EMAIL>>", "<PERSON> <<EMAIL>>", "sbertal <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "sourabh2k15 <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <32746416+step<PERSON><PERSON><PERSON><EMAIL>>", "swiftfoot <<EMAIL>>", "<PERSON> <<EMAIL>>", "telaoumatenyanis <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> (TIJ) <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "zouxuoz <<EMAIL>>"], "gitHead": "773d6ebf881357f6e4c2dd7e8984b1bd0f69b4ca", "homepage": "https://formatjs.github.io/docs/react-intl", "keywords": ["format", "formatting", "globalization", "i18n", "internationalization", "intl", "locale", "localization", "react", "reactjs", "translate", "translation"], "main": "index.js", "module": "lib/index.js", "peerDependenciesMeta": {"typescript": {"optional": true}}, "repository": "**************:formatjs/formatjs.git"}