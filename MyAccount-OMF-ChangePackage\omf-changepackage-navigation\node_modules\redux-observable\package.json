{"name": "redux-observable", "version": "3.0.0-rc.2", "description": "RxJS based middleware for Redux. Compose and cancel async actions and more.", "main": "dist/cjs/redux-observable.cjs", "module": "dist/redux-observable.legacy-esm.js", "types": "dist/redux-observable.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/redux-observable.d.ts", "import": "./dist/redux-observable.mjs", "default": "./dist/cjs/redux-observable.cjs"}}, "sideEffects": false, "scripts": {"lint": "eslint . --ext .ts", "build": "tsup"}, "files": ["dist", "src", "README.md", "LICENSE"], "repository": {"type": "git", "url": "git+https://github.com/redux-observable/redux-observable.git"}, "keywords": ["Rx", "Ducks", "Reducks", "Redux", "middleware", "observable", "thunk", "async", "cancel", "action"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/redux-observable/redux-observable/issues"}, "homepage": "https://github.com/redux-observable/redux-observable#README.md", "peerDependencies": {"redux": ">=5 <6", "rxjs": ">=7 <8"}, "devDependencies": {"@types/chai": "^4.3.11", "@types/mocha": "^10.0.6", "@types/node": "^20.10.4", "@types/sinon": "^17.0.2", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "chai": "^4.3.10", "conventional-changelog-cli": "^4.1.0", "eslint": "^8.55.0", "eslint-plugin-rxjs": "^5.0.3", "mocha": "^10.2.0", "redux": "^5.0.0", "rxjs": "^7.8.1", "sinon": "^17.0.1", "tsup": "7.0.0", "typescript": "^5.3.3"}}