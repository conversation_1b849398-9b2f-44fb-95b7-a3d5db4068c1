{"compilerOptions": {"target": "ES5", "module": "ESNext", "moduleResolution": "node", "lib": ["DOM", "DOM.Iterable", "ES5"], "alwaysStrict": true, "disableSizeLimit": true, "downlevelIteration": true, "sourceMap": true, "allowSyntheticDefaultImports": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "removeComments": true, "forceConsistentCasingInFileNames": true, "noUnusedLocals": true, "pretty": true, "strictNullChecks": true, "noImplicitAny": true, "noImplicitReturns": true, "jsx": "react", "importHelpers": true, "types": ["node"]}}